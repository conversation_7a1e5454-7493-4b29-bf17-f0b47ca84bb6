@tailwind base;
@tailwind components;
@tailwind utilities;
 
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 262.1 83.3% 57.8%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 262.1 83.3% 57.8%;
    --radius: 0.75rem;
  }
 
  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 263.4 95.2% 50.2%;
    --primary-foreground: 210 20% 98%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 263.4 95.2% 50.2%;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }

  /* AGGRESSIVE SCROLLBAR STYLING - FUCK THE WHITE SCROLLBAR */
  ::-webkit-scrollbar {
    width: 8px !important;
    height: 8px !important;
  }

  ::-webkit-scrollbar-track {
    background: #1a1a1a !important;
    border-radius: 0 !important;
  }

  ::-webkit-scrollbar-thumb {
    background: #404040 !important;
    border-radius: 4px !important;
    border: none !important;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #606060 !important;
  }

  ::-webkit-scrollbar-corner {
    background: #1a1a1a !important;
  }

  /* Firefox scrollbar */
  html {
    scrollbar-width: thin !important;
    scrollbar-color: #404040 #1a1a1a !important;
  }

  /* Apply to ALL elements with maximum specificity */
  *, *::before, *::after {
    scrollbar-width: thin !important;
    scrollbar-color: #404040 #1a1a1a !important;
  }

  /* Target specific containers that might override */
  body, html, div, main, section, article {
    scrollbar-width: thin !important;
    scrollbar-color: #404040 #1a1a1a !important;
  }

  body::-webkit-scrollbar,
  html::-webkit-scrollbar,
  div::-webkit-scrollbar,
  main::-webkit-scrollbar {
    width: 8px !important;
    height: 8px !important;
  }

  body::-webkit-scrollbar-track,
  html::-webkit-scrollbar-track,
  div::-webkit-scrollbar-track,
  main::-webkit-scrollbar-track {
    background: #1a1a1a !important;
  }

  body::-webkit-scrollbar-thumb,
  html::-webkit-scrollbar-thumb,
  div::-webkit-scrollbar-thumb,
  main::-webkit-scrollbar-thumb {
    background: #404040 !important;
    border-radius: 4px !important;
  }
}

@keyframes sweep {
  from {
    background-position: 200% center;
  }
  to {
    background-position: -200% center;
  }
}

.gradient-text-honda {
  @apply bg-gradient-to-r from-[#826BC2] to-[#3c3c3c] bg-clip-text text-transparent;
  background-size: 200% auto;
  animation: sweep 4s linear infinite;
}

.dark .gradient-text-honda {
  @apply bg-gradient-to-r from-[#a28ee2] to-[#9e9e9e] bg-clip-text text-transparent;
}

.gradient-text-lunarbine {
  @apply bg-gradient-to-r from-[#FFCCDB] to-[#FF7FA8] bg-clip-text text-transparent;
  background-size: 200% auto;
  animation: sweep 4s linear infinite;
}

.dark .gradient-text-lunarbine {
  @apply bg-gradient-to-r from-[#ffdae5] to-[#ff9fbe] bg-clip-text text-transparent;
}

.main-bg {
  @apply bg-background;
  background-image: radial-gradient(circle at 25px 25px, hsl(var(--primary) / 0.05) 2%, transparent 0%), 
                    radial-gradient(circle at 75px 75px, hsl(var(--primary) / 0.05) 2%, transparent 0%);
  background-size: 100px 100px;
}